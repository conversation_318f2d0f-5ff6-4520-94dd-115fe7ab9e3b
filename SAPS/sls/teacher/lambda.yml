TeacherManagement:
  handler: src/presentation/controllers/teacher/interface.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-TeacherManagement-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: teacher
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: teacher/{id}
        method: put
        cors: true
        authorizer: authorizer 