PlaylistManagement:
  handler: src/presentation/controllers/playlist/interface.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-PlaylistManagement-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: playlist
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: playlist/update/{playlistId}
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: playlist/addVideos/{playlistId}
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: playlist/get/{playlistId}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: playlist/filter/single
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: playlist/filter
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: playlist/type/{type}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: playlist/user/{ownerId}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: playlist/{playlistId}
        method: delete
        cors: true
        authorizer: authorizer 