VideoManagement:
  handler: src/presentation/controllers/video/interface.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-VideoManagement-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - s3:PutObject
        - s3:GetObject
        - s3:DeleteObject
        - execute-api:Invoke
        - sqs:*
      Resource: "*"
  events:
    - http:
        path: video
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: video
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: video/like
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: video/like
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: video/save
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: video/save
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: video/comment
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: video/comment
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: video/comment/like
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: video/comment/like
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: video/watched
        method: post
        cors: true
        authorizer: authorizer
    - http:
        path: video/likedBy/{userId}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: video/savedBy/{userId}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: video/watchedBy/{userId}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: video/subject/{subject}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: video/get/base/{id}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: video/get/withStats/{id}
        method: get
        cors: true
        authorizer: authorizer
    - http:
        path: video/{videoId}
        method: delete
        cors: true
        authorizer: authorizer
    - http:
        path: video/comments/group/{groupId}
        method: get
        cors: true
        authorizer: authorizer

GetCommentsByGroupsBatch:
  handler: src/presentation/handlers/video/getCommentsByGroupsBatch.handler
  memorySize: 1024
  timeout: 30
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetCommentsByGroupsBatch-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/comments/groups/batch
        method: post
        cors: true
        authorizer: authorizer

GetCommentInfo:
  handler: src/presentation/handlers/video/getCommentInfo.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-GetCommentInfo-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
      Resource: "*"
  events:
    - http:
        path: video/comment/{commentId}/info
        method: get
        cors: true
        authorizer: authorizer

RespondToDoubt:
  handler: src/presentation/handlers/video/respondToDoubt.handler
  memorySize: 1024
  maximumRetryAttempts: 0
  iamRoleStatementsName: ${self:service}-${self:custom.stage}-RespondToDoubt-role
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - rds-data:ExecuteStatement
        - rds-data:BatchExecuteStatement
        - execute-api:Invoke
      Resource: "*"
  events:
    - http:
        path: video/comment/{commentId}/respond
        method: post
        cors: true
        authorizer: authorizer