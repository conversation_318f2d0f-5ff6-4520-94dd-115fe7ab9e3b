import { apiResponse } from "../../../../utils/apiResponse.js";
import { InvalidInputValueError } from "../../../../utils/customErrors/index.js";
import { handler as createT<PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../handlers/teacher/createTeacher.js";
import { handler as updateTeacher<PERSON>andler } from "../../handlers/teacher/updateTeacher.js";

export async function handler(event) {
  try {
    if (event.source === "serverless-plugin-warmup") {
      return Promise.resolve("Lambda warmed up!");
    }

    console.time("Teacher Management Duration");
    const functions = {
      "/teacher": {
        POST: createTeacher<PERSON><PERSON><PERSON>
      },
      "/teacher/{id}": {
        PUT: updateTeacherHandler
      }
    };

    const resource = event.resource;
    const method = event.httpMethod;

    if (!functions[resource] || !functions[resource][method]) {
      throw new InvalidInputValueError(`Interface not mapped! for relative endpoint ${resource} with method : ${method}`);
    }

    const response = await functions[resource][method](event);
    console.log({response});
    console.timeEnd("Teacher Management Duration");
    return response;
  } catch (error) {
    console.log("TeacherManagement", error, event);
    return Promise.resolve(
      apiResponse(error.code || 500, {
        body: {
          error: error.name || "UnexpectedError",
          message: error.message || error,
          formattedMessage:
            error.formattedMessage || "An unexpected error occurred.",
        },
      })
    );
  }
}
