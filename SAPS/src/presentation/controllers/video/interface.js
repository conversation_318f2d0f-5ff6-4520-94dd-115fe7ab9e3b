import { apiResponse } from "../../../../utils/apiResponse.js";
import { InvalidInputValueError } from "../../../../utils/customErrors/index.js";
import { handler as createVideoHandler } from "../../handlers/video/createVideo.js";
import { handler as addLikeToVideoHandler } from "../../handlers/video/addLikeToVideo.js";
import { handler as addSaveToVideoHandler } from "../../handlers/video/addSaveToVideo.js";
import { handler as addCommentHandler } from "../../handlers/video/addComment.js";
import { handler as addLikeToCommentHandler } from "../../handlers/video/addLikeToComment.js";
import { handler as addWatchedToVideoHandler } from "../../handlers/video/addWatchedToVideo.js";
import { handler as getVideosHandler } from "../../handlers/video/getVideos.js";
import { handler as getLikedVideosHandler } from "../../handlers/video/getLikedVideos.js";
import { handler as getSavedVideosHandler } from "../../handlers/video/getSavedVideos.js";
import { handler as getWatchedVideosHandler } from "../../handlers/video/getWatchedVideos.js";
import { handler as getVideosBySubjectHandler } from "../../handlers/video/getBySubject.js";
import { handler as getVideoByIdHandler } from "../../handlers/video/getVideoById.js";
import { handler as getVideoWithStatsHandler } from "../../handlers/video/getVideoWithStats.js";
import { handler as deleteVideoHandler } from "../../handlers/video/deleteVideo.js";
import { handler as removeCommentLikeHandler } from "../../handlers/video/removeCommentLike.js";
import { handler as deleteCommentHandler } from "../../handlers/video/deleteComment.js";
import { handler as removeVideoLikeHandler } from "../../handlers/video/removeVideoLike.js";
import { handler as removeVideoSaveHandler } from "../../handlers/video/removeVideoSave.js";
import { handler as getCommentsByGroupHandler } from "../../handlers/video/getCommentsByGroup.js";

export async function handler(event) {
  try {
    if (event.source === "serverless-plugin-warmup") {
      return Promise.resolve("Lambda warmed up!");
    }

    console.time("Video Management Duration");
    const functions = {
      "/video": {
        POST: createVideoHandler,
        GET: getVideosHandler
      },
      "/video/like": {
        POST: addLikeToVideoHandler,
        DELETE: removeVideoLikeHandler
      },
      "/video/save": {
        POST: addSaveToVideoHandler,
        DELETE: removeVideoSaveHandler
      },
      "/video/comment": {
        POST: addCommentHandler,
        DELETE: deleteCommentHandler
      },
      "/video/comment/like": {
        POST: addLikeToCommentHandler,
        DELETE: removeCommentLikeHandler
      },
      "/video/watched": {
        POST: addWatchedToVideoHandler
      },
      "/video/likedBy/{userId}": {
        GET: getLikedVideosHandler
      },
      "/video/savedBy/{userId}": {
        GET: getSavedVideosHandler
      },
      "/video/watchedBy/{userId}": {
        GET: getWatchedVideosHandler
      },
      "/video/subject/{subject}": {
        GET: getVideosBySubjectHandler
      },
      "/video/get/base/{id}": {
        GET: getVideoByIdHandler
      },
      "/video/get/withStats/{id}": {
        GET: getVideoWithStatsHandler
      },
      "/video/{videoId}": {
        DELETE: deleteVideoHandler
      },
      "/video/comments/group/{groupId}": {
        GET: getCommentsByGroupHandler
      }
    };

    const resource = event.resource;
    const method = event.httpMethod;

    if (!functions[resource] || !functions[resource][method]) {
      throw new InvalidInputValueError(`Interface not mapped! for relative endpoint ${resource} with method : ${method}`);
    }

    const response = await functions[resource][method](event);
    console.log({response});
    console.timeEnd("Video Management Duration");
    return response;
  } catch (error) {
    console.log("VideoManagement", error, event);
    return Promise.resolve(
      apiResponse(error.code || 500, {
        body: {
          error: error.name || "UnexpectedError",
          message: error.message || error,
          formattedMessage:
            error.formattedMessage || "An unexpected error occurred.",
        },
      })
    );
  }
}
