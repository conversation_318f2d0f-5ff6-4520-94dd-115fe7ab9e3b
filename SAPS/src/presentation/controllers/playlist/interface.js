import { apiResponse } from "../../../../utils/apiResponse.js";
import { InvalidInputValueError } from "../../../../utils/customErrors/index.js";
import { handler as createPlaylistHandler } from "../../handlers/playlist/createPlaylist.js";
import { handler as updatePlaylistHandler } from "../../handlers/playlist/updatePlaylist.js";
import { handler as addVideoToPlaylistHandler } from "../../handlers/playlist/addVideoToPlaylist.js";
import { handler as getPlaylistHandler } from "../../handlers/playlist/getPlaylist.js";
import { handler as getSinglePlaylistByFilterHandler } from "../../handlers/playlist/getSinglePlaylistByFilter.js";
import { handler as getPlaylistsByFilterHandler } from "../../handlers/playlist/getPlaylistsByFilter.js";
import { handler as getPlaylistByTypeHandler } from "../../handlers/playlist/getPlaylistByType.js";
import { handler as getPlaylistByOwnerHandler } from "../../handlers/playlist/getPlaylistByOwner.js";
import { handler as deletePlaylistHandler } from "../../handlers/playlist/deletePlaylist.js";

export async function handler(event) {
  try {
    if (event.source === "serverless-plugin-warmup") {
      return Promise.resolve("Lambda warmed up!");
    }

    console.time("Playlist Management Duration");
    const functions = {
      "/playlist": {
        POST: createPlaylistHandler
      },
      "/playlist/update/{playlistId}": {
        POST: updatePlaylistHandler
      },
      "/playlist/addVideos/{playlistId}": {
        POST: addVideoToPlaylistHandler
      },
      "/playlist/get/{playlistId}": {
        GET: getPlaylistHandler
      },
      "/playlist/filter/single": {
        GET: getSinglePlaylistByFilterHandler
      },
      "/playlist/filter": {
        GET: getPlaylistsByFilterHandler
      },
      "/playlist/type/{type}": {
        GET: getPlaylistByTypeHandler
      },
      "/playlist/user/{ownerId}": {
        GET: getPlaylistByOwnerHandler
      },
      "/playlist/{playlistId}": {
        DELETE: deletePlaylistHandler
      }
    };

    const resource = event.resource;
    const method = event.httpMethod;

    if (!functions[resource] || !functions[resource][method]) {
      throw new InvalidInputValueError(`Interface not mapped! for relative endpoint ${resource} with method : ${method}`);
    }

    const response = await functions[resource][method](event);
    console.log({response});
    console.timeEnd("Playlist Management Duration");
    return response;
  } catch (error) {
    console.log("PlaylistManagement", error, event);
    return Promise.resolve(
      apiResponse(error.code || 500, {
        body: {
          error: error.name || "UnexpectedError",
          message: error.message || error,
          formattedMessage:
            error.formattedMessage || "An unexpected error occurred.",
        },
      })
    );
  }
}
