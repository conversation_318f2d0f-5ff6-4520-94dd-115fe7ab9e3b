import { getWatchedVideosService } from "../../../application/services/video/getWatchedVideosService.js";
import { apiResponse } from "../../../../utils/apiResponse.js";

export async function handler(event) {
  try {
    const page = parseInt(event.queryStringParameters?.page) || 0;
    const limit = parseInt(event.queryStringParameters?.limit) || 10;
    const { userId } = event.pathParameters;

    if (!userId) {
      return apiResponse(400, { body: "User ID is required" });
    }

    const result = await getWatchedVideosService({ page, limit, userId });
    return apiResponse(200, { body: result });
  } catch (error) {
    console.log(error)   
    return apiResponse(error.statusCode || 500, { body: error });
  }
}
