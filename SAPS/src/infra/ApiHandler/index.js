class ApiCaller {
    constructor(baseURL) {
        this.baseURL = baseURL;
    }

    getHeaders(headers, token) {
        if (token.includes('Bearer ')) {
            token = token.split(' ')[1];
        }

        return token
            ? {
                ...headers,
                Authorization: `Bearer ${token}`
            }
            : headers;
    }

    async _request(method, endpoint, { body = null, headers = {}, token = null } = {}) {
        try {
            console.log(`Calling path: ${this.baseURL}${endpoint} with method ${method}`);
            const response = await fetch(`${this.baseURL}${endpoint}`, {
                method,
                headers: this.getHeaders({
                    'Content-Type': 'application/json',
                    ...headers
                }, token),
                body: body ? JSON.stringify(body) : null
            });

            if (!response.ok) {
                throw new Error(`${method} request failed with status ${response.status}: ${response.statusText}`);
            }

            const contentType = response.headers.get("Content-Type");
            if (contentType && contentType.includes("application/json")) {
                return await response.json();
            } else if (contentType && contentType.includes("text/plain")) {
                const text = await response.text();
                return text === "true" ? true : text === "false" ? false : text;
            } else {
                // Fallback: if no content-type and body matches boolean
                const text = await response.text();
                return text === "true" ? true : text === "false" ? false : text;
            }
        } catch (error) {
            console.error(`Error during ${method} request: ${error.message}`);
            throw error;
        }
    }

    async get(endpoint, headers = {}, token = null) {
        return this._request('GET', endpoint, { headers, token });
    }

    async post(endpoint, body = {}, headers = {}, token = null) {
        return this._request('POST', endpoint, { body, headers, token });
    }

    async put(endpoint, body = {}, headers = {}, token = null) {
        return this._request('PUT', endpoint, { body, headers, token });
    }

    async patch(endpoint, body = {}, headers = {}, token = null) {
        return this._request('PATCH', endpoint, { body, headers, token });
    }

    async delete(endpoint, headers = {}, token = null) {
        return this._request('DELETE', endpoint, { headers, token });
    }
}

export default ApiCaller;