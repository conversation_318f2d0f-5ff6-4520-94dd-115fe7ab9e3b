export type MessageType = 'text' | 'image' | 'file' | 'system';

export interface MessageAuthor {
  id: string;
  name: string;
  avatarUrl?: string;
}

export interface Message {
  id: string; // server id
  channelId: string;
  order: number; // monotonically increasing per channel
  type: MessageType;
  text?: string;
  author: MessageAuthor;
  createdAt: string; // ISO
  editedAt?: string; // ISO
  deletedAt?: string; // ISO
  metadata?: Record<string, any>;
  attachments?: Array<{
    id: string;
    url: string;
    name: string;
    mimeType: string;
    size?: number;
    width?: number;
    height?: number;
  }>;
}

export interface MessagesResponseBody {
  data: {
    messages: Message[];
    pagination?: {
      limit?: number;
      offset?: number;
      total?: number;
    };
  };
}

export interface SendMessageJsonBody {
  channelId: string;
  messageType: 'text' | 'image' | 'file';
  text: string; // Required - must be non-empty string for text messages
  replyTo?: string;
  metadata?: Record<string, any> & { attachments?: Array<{ url: string; name: string; mimeType: string; size?: number }> };
}

export interface SendMessageResponse {
  data: {
    message: Message;
  }
}

