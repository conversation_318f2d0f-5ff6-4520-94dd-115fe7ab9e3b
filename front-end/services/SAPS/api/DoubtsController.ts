import ApiCaller from './ApiCaller';

export interface IDoubtComment {
    id: number;
    content: string;
    userId: string;
    username: string;
    videoId: number;
    videoTitle?: string;
    parentCommentId?: number;
    doubtStatus: -1 | 0 | 1; // -1: não respondida, 0: alguém pegou, 1: respondida
    createdAt: string;
    updatedAt: string;
    video?: {
        id: number;
        title: string;
        description: string;
    };
}

export interface IDoubtsResponse {
    comments: IDoubtComment[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}

export interface IBatchDoubtsRequest {
    groupIds: string[];
    videoId?: string;
    page?: number;
    limit?: number;
}

export interface IBatchDoubtsResponse {
    comments: IDoubtComment[];
    groupResults: {
        groupId: string;
        success: boolean;
        error?: string;
        commentCount: number;
    }[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}

export default class DoubtsControllerOptimized extends ApiCaller {
    /**
     * Busca comentários/dúvidas de um grupo específico
     */
    static async getCommentsByGroup(
        groupId: string,
        options?: {
            videoId?: string;
            isDoubt?: boolean;
            page?: number;
            limit?: number;
        }
    ): Promise<IDoubtsResponse> {
        const params = new URLSearchParams();
        
        if (options?.videoId) params.append('videoId', options.videoId);
        if (options?.isDoubt !== undefined) params.append('isDoubt', String(options.isDoubt));
        if (options?.page) params.append('page', String(options.page));
        if (options?.limit) params.append('limit', String(options.limit));

        const queryString = params.toString() ? `?${params.toString()}` : '';
        
        return await this.apiCall(`/video/comments/group/${groupId}${queryString}`, {
            method: 'GET'
        });
    }

    /**
     * 🚀 OPTIMIZED: Busca dúvidas de múltiplos grupos em uma única requisição
     * Esta versão usa um endpoint batch para reduzir o número de chamadas de API
     */
    static async getDoubtsFromMultipleGroupsBatch(
        groupIds: string[],
        options?: {
            videoId?: string;
            page?: number;
            limit?: number;
        }
    ): Promise<IDoubtComment[]> {
        if (!groupIds || groupIds.length === 0) return [];

        // Remove duplicados e falsy
        const uniqueGroupIds = Array.from(new Set(groupIds.filter(Boolean)));
        
        try {
            console.log(`[DoubtsController] Batch request for ${uniqueGroupIds.length} groups`);
            
            const batchRequest: IBatchDoubtsRequest = {
                groupIds: uniqueGroupIds,
                ...options
            };

            const response: IBatchDoubtsResponse = await this.apiCall('/video/comments/groups/batch', {
                method: 'POST',
                data: batchRequest
            });

            // Log any group-specific errors
            response.groupResults?.forEach(result => {
                if (!result.success) {
                    if (process.env.NODE_ENV !== 'production') {
                        console.debug(`[DoubtsController] Group ${result.groupId} failed: ${result.error}`);
                    }
                }
            });

            return response.comments.sort(
                (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
            );

        } catch (error: any) {
            console.error('[DoubtsController] Batch request failed, falling back to individual requests:', error);
            
            // Fallback to individual requests if batch endpoint is not available
            return await this.getDoubtsFromMultipleGroupsFallback(uniqueGroupIds, options);
        }
    }

    /**
     * 🔄 IMPROVED: Versão otimizada das requisições individuais com melhor controle de erro
     */
    static async getDoubtsFromMultipleGroupsFallback(
        groupIds: string[],
        options?: {
            videoId?: string;
            page?: number;
            limit?: number;
        }
    ): Promise<IDoubtComment[]> {
        if (!groupIds || groupIds.length === 0) return [];

        const uniqueGroupIds = Array.from(new Set(groupIds.filter(Boolean)));
        const allDoubts: IDoubtComment[] = [];
        
        console.log(`[DoubtsController] Fallback: Making ${uniqueGroupIds.length} individual requests`);
        
        // Limit concurrent requests to avoid overwhelming the server
        const BATCH_SIZE = 5;
        const batches = [];
        
        for (let i = 0; i < uniqueGroupIds.length; i += BATCH_SIZE) {
            batches.push(uniqueGroupIds.slice(i, i + BATCH_SIZE));
        }
        
        for (const batch of batches) {
            const batchResults = await Promise.allSettled(
                batch.map(async (groupId) => {
                    try {
                        const response = await this.getCommentsByGroup(groupId, {
                            ...options,
                            isDoubt: true
                        });
                        return response.comments || [];
                    } catch (error: any) {
                        // Silencia 403 (usuário não membro) para evitar ruído em logs e UX
                        if (error?.response?.status === 403) {
                            if (process.env.NODE_ENV !== 'production') {
                                console.debug(`[DoubtsController] 403 ignorado para grupo ${groupId} (não membro)`);
                            }
                            return [];
                        }
                        console.error(`Erro ao buscar dúvidas do grupo ${groupId}:`, error);
                        return [];
                    }
                })
            );
            
            batchResults.forEach(result => {
                if (result.status === 'fulfilled') {
                    allDoubts.push(...result.value);
                }
            });
            
            // Small delay between batches to be nice to the server
            if (batches.indexOf(batch) < batches.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        return allDoubts.sort(
            (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
    }
    static async getDoubtsFromMultipleGroupsCached(
        groupIds: string[],
        options?: {
            videoId?: string;
            page?: number;
            limit?: number;
            cacheTimeout?: number; // milliseconds
        }
    ): Promise<IDoubtComment[]> {
        const cacheKey = `doubts_${groupIds.sort().join('_')}_${JSON.stringify(options)}`;
        const cacheTimeout = options?.cacheTimeout || 30000; // 30 seconds default
        
        // Check cache
        const cached = this.getFromCache(cacheKey);
        if (cached && Date.now() - cached.timestamp < cacheTimeout) {
            console.log('[DoubtsController] Returning cached results');
            return cached.data;
        }
        
        // Fetch fresh data
        const doubts = await this.getDoubtsFromMultipleGroupsBatch(groupIds, options);
        
        // Cache results
        this.setCache(cacheKey, {
            data: doubts,
            timestamp: Date.now()
        });
        
        return doubts;
    }

    /**
     * Simple in-memory cache implementation
     */
    private static cache = new Map<string, { data: any; timestamp: number }>();
    
    private static getFromCache(key: string) {
        return this.cache.get(key);
    }
    
    private static setCache(key: string, value: { data: any; timestamp: number }) {
        // Limit cache size
        if (this.cache.size > 100) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }

    /**
     * Clear cache manually if needed
     */
    static clearCache() {
        this.cache.clear();
    }

    static async getDoubtsFromMultipleGroups(
        groupIds: string[],
        options?: {
            videoId?: string;
            page?: number;
            limit?: number;
        }
    ): Promise<IDoubtComment[]> {
        console.warn('[DoubtsController] Using legacy getDoubtsFromMultipleGroups. Consider upgrading to getDoubtsFromMultipleGroupsBatch for better performance.');

        return await this.getDoubtsFromMultipleGroupsFallback(groupIds, options);
    }

    /**
     * Gets comment information directly from SAPS database
     * This bypasses CDS enrichment issues and returns actual comment content
     */
    static async getCommentInfo(commentId: number): Promise<{
        id: number;
        content: string;
        isDoubt: boolean;
        userId: string;
        username: string;
        createdAt: string;
        videoId: number;
        video: {
            id: number;
            title: string;
            subject: string;
            teacher: {
                id: string;
                name: string;
            };
        };
        playlistId?: string;
    }> {
        console.log(`[DoubtsController] Getting comment info for ${commentId}`);

        try {
            const response = await this.apiCall(`/video/comment/${commentId}/info`, {
                method: 'GET'
            });

            console.log(`[DoubtsController] Successfully retrieved comment info for ${commentId}`);
            return response;
        } catch (error: any) {
            console.error(`[DoubtsController] Failed to get comment info for ${commentId}:`, error);
            throw error;
        }
    }

    /**
     * Enriches doubt comments with actual content from SAPS
     * This fixes the placeholder content issue by fetching real content
     */
    static async enrichDoubtsWithActualContent(doubts: IDoubtComment[]): Promise<IDoubtComment[]> {
        if (!doubts || doubts.length === 0) return doubts;

        console.log(`[DoubtsController] Enriching ${doubts.length} doubts with actual content`);

        const enrichedDoubts = await Promise.allSettled(
            doubts.map(async (doubt) => {
                // Skip if content is already valid (not placeholder)
                if (doubt.content &&
                    !doubt.content.includes('temporarily unavailable') &&
                    !doubt.content.includes('Invalid comment ID') &&
                    !(doubt.content.startsWith('[') && doubt.content.endsWith(']'))) {
                    return doubt;
                }

                try {
                    // Get actual content from SAPS
                    const commentInfo = await this.getCommentInfo(doubt.id);

                    return {
                        ...doubt,
                        content: commentInfo.content,
                        video: commentInfo.video ? {
                            id: commentInfo.video.id,
                            title: commentInfo.video.title,
                            description: commentInfo.video.subject || ''
                        } : doubt.video
                    };
                } catch (error) {
                    console.warn(`[DoubtsController] Failed to enrich doubt ${doubt.id}, keeping original:`, error);
                    return doubt;
                }
            })
        );

        const results = enrichedDoubts
            .filter(result => result.status === 'fulfilled')
            .map(result => (result as PromiseFulfilledResult<IDoubtComment>).value);

        console.log(`[DoubtsController] Successfully enriched ${results.length} doubts`);
        return results;
    }

    /**
     * Initiates teacher response to a doubt
     * This will:
     * 1. Update doubt status from -1 to 0 (teacher responding)
     * 2. Create a communication channel for teacher-student chat
     * 3. Return channel information for frontend to open chat
     */
    static async respondToDoubt(
        commentId: number,
    ): Promise<{
        success: boolean;
        message: string;
        data: {
            commentId: number;
            channelId: string;
            channelName: string;
            studentId: string;
            studentName: string;
            videoTitle?: string;
            doubtContent: string;
        };
    }> {
        console.log(`[DoubtsController] Initiating response to doubt ${commentId}`);

        try {
            const response = await this.apiCall(`/video/comment/${commentId}/respond`, {
                method: 'POST'
            });

            console.log(`[DoubtsController] Successfully initiated response to doubt ${commentId}`);
            return response;
        } catch (error: any) {
            console.error(`[DoubtsController] Failed to respond to doubt ${commentId}:`, error);
            throw error;
        }
    }
}
