import { sendMessage } from '../useCases/sendMessage.js';
import { ValidationError, AuthorizationError, NotFoundError } from '../../../utils/customErrors/index.js';
import { apiResponse } from '../../../utils/apiResponse.js';
import connectToDB from '../../../infra/libs/mongodb/connect.js';

export async function handler(event) {
  try {
    await connectToDB();

    // Extract user context from authorizer
    const { id: userId, role } = event.requestContext.authorizer || {};
    
    if (!userId) {
      return apiResponse(401, { error: 'Unauthorized: User ID not found' });
    }

    let body, imageFile;

    const contentType = event.headers['Content-Type'] || event.headers['content-type'] || '';
    
    if (contentType.startsWith('multipart/form-data')) {
      const parseMultipart = await import('../../../utils/parsers/multipartParser.js');
      const parsed = parseMultipart.default(event.body, contentType);
      
      body = {};
      
      // Extract text fields
      if (parsed.fields.text) body.text = parsed.fields.text;
      if (parsed.fields.channelId) body.channelId = parsed.fields.channelId;
      if (parsed.fields.messageType) body.messageType = parsed.fields.messageType;
      if (parsed.fields.replyTo) body.replyTo = parsed.fields.replyTo;
      if (parsed.fields.metadata) {
        try {
          body.metadata = JSON.parse(parsed.fields.metadata);
        } catch (e) {
          return apiResponse(400, { error: 'Invalid JSON in metadata field' });
        }
      }

      // Extract image file if present
      if (parsed.files && parsed.files.image) {
        const file = parsed.files.image;
        imageFile = {
          buffer: Buffer.from(file.data),
          mimeType: file.mimeType,
          originalName: file.filename
        };
      }
    } else {
      // Handle regular JSON request
      try {
        body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
      } catch (error) {
        return apiResponse(400, { error: 'Invalid JSON in request body' });
      }
    }

    const { 
      text,
      channelId,
      messageType,
      replyTo,
      metadata
    } = body;

    // Send message w use case
    const result = await sendMessage({
      userId,
      role,
      text,
      channelId,
      messageType,
      replyTo,
      imageFile,
      metadata
    });

    return apiResponse(201, { body: result });

  } catch (error) {
    console.error('Error in sendMessage handler:', error);

    if (error instanceof ValidationError) {
      return apiResponse(400, { error: error.message });
    }

    if (error instanceof AuthorizationError) {
      return apiResponse(403, { error: error.message });
    }

    if (error instanceof NotFoundError) {
      return apiResponse(404, { error: error.message });
    }

    return apiResponse(500, { 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
