import CommunicationRepository from '../repositories/communicationRepository.js';
import { ValidationError } from '../../../utils/customErrors/index.js';

const communicationRepository = new CommunicationRepository();

export const getSelfChannels = async ({ 
  userId, 
  role,
  type,
  limit = 50,
  offset = 0,
  sortBy = 'lastMessageAt',
  sortOrder = 'desc'
}) => {
  try {
    if (!userId) {
      throw new ValidationError('userId is required');
    }

    const parsedLimit = Math.min(parseInt(limit, 10) || 50, 100); // Max 100 channels
    const parsedOffset = Math.max(parseInt(offset, 10) || 0, 0);
    const parsedSortOrder = sortOrder === 'asc' ? 1 : -1;

    // Validate type parameter
    if (type && !['GROUP', 'CHAT'].includes(type)) {
      throw new ValidationError('type must be either GROUP or CHAT');
    }

    // Validate sortBy parameter
    const allowedSortFields = ['lastMessageAt', 'createdAt', 'name'];
    if (!allowedSortFields.includes(sortBy)) {
      throw new ValidationError(`sortBy must be one of: ${allowedSortFields.join(', ')}`);
    }

    // Get channels with filters
    const options = {
      type,
      limit: parsedLimit,
      offset: parsedOffset,
      sortBy,
      sortOrder: parsedSortOrder
    };

    const channels = await communicationRepository.getChannelsByUser(userId, options);

    const formattedChannels = channels.map(channel => ({
      id: channel._id.toString(), // Frontend expects 'id'
      channelId: channel._id.toString(), // Keep for backward compatibility
      name: channel.name,
      type: channel.type,
      contextType: channel.contextType,
      contextMetadata: channel.contextMetadata,
      members: channel.members.map(member => ({
        id: member._id.toString(), // Frontend expects 'id'
        userId: member._id.toString(), // Keep for backward compatibility
        name: member.name,
        email: member.email,
        role: member.role
      })),
      owner: {
        id: channel.ownerId._id.toString(), // Frontend expects 'id'
        userId: channel.ownerId._id.toString(), // Keep for backward compatibility
        name: channel.ownerId.name,
        email: channel.ownerId.email,
        role: channel.ownerId.role
      },
      messageCount: channel.messageCount,
      lastMessageAt: channel.lastMessageAt,
      createdAt: channel.createdAt,
      updatedAt: channel.updatedAt
    }));

    return {
      success: true,
      message: 'Channels retrieved successfully',
      data: {
        channels: formattedChannels,
        pagination: {
          limit: parsedLimit,
          offset: parsedOffset,
          count: channels.length,
          hasMore: channels.length === parsedLimit
        }
      }
    };

  } catch (error) {
    if (error instanceof ValidationError) {
      throw error;
    }
    
    console.error('Unexpected error in getSelfChannels:', error);
    throw new Error(`Failed to get channels: ${error.message}`);
  }
};
