import CommunicationRepository from '../repositories/communicationRepository.js';
import { LessonMetricModel } from '../../../domain/models/metrics/lessonMetricsModel.js';
import { ValidationError, AuthorizationError } from '../../../utils/customErrors/index.js';

const communicationRepository = new CommunicationRepository();

export const createChannel = async ({ 
  userId, 
  role,
  name,
  members = [],
  groupIds = [],
  type = 'CHAT',
  contextType = 'NONE',
  contextId
}) => {
  try {
    if (!userId) {
      throw new ValidationError('userId is required');
    }
    
    if (!name) {
      throw new ValidationError('name is required');
    }

    if (typeof name !== 'string' || name.trim().length === 0) {
      throw new ValidationError('name must be a non-empty string');
    }

    if (name.length > 100) {
      throw new ValidationError('name must be 100 characters or less');
    }

    if (!['GROUP', 'CHAT'].includes(type)) {
      throw new ValidationError('type must be either GROUP or CHAT');
    }

    if (!['DOUBT', 'NONE', 'ANNOUNCEMENT'].includes(contextType)) {
      throw new ValidationError('contextType must be a valid enum value');
    }

    if (members && !Array.isArray(members)) {
      throw new ValidationError('members must be an array');
    }

    if (groupIds && !Array.isArray(groupIds)) {
      throw new ValidationError('groupIds must be an array');
    }

    if (contextType === 'DOUBT') {
      if (!contextId) {
        throw new ValidationError('contextId is required for DOUBT channels');
      }

      // Check if there's already a channel for this doubt
      const existingChannels = await communicationRepository.getChannelsByContext({
        contextType: 'DOUBT',
        contextId
      });

      if (existingChannels.length > 0) {
        throw new ValidationError('A channel for this doubt already exists');
      }

      // Find the lesson metric containing this doubt
      const lessonMetric = await LessonMetricModel.findOne({
        'doubts.commentId': contextId
      });

      if (!lessonMetric) {
        throw new ValidationError('Doubt not found in lesson metrics');
      }

      // Find the specific doubt
      const doubt = lessonMetric.doubts.find(d => d.commentId === contextId);
      
      if (!doubt) {
        throw new ValidationError('Doubt not found');
      }

      // Check if doubt status allows channel creation (must be -1)
      if (doubt.status !== -1) {
        throw new ValidationError('This doubt is already being answered or has been resolved');
      }
    }

    const channelData = {
      name: name.trim(),
      members: [...new Set([userId, ...members])],
      groupIds,
      type,
      contextType,
      contextId,
      ownerId: userId
    };

    const createdChannel = await communicationRepository.createChannel(channelData);

    if (contextType === 'DOUBT' && contextId) {
      const lessonMetric = await LessonMetricModel.findOne({
        'doubts.commentId': contextId
      });

      if (lessonMetric) {
        const doubtIndex = lessonMetric.doubts.findIndex(d => d.commentId === contextId);
        if (doubtIndex !== -1) {
          lessonMetric.doubts[doubtIndex].status = 0;
          await lessonMetric.save();
        }
      }
    }


    // Format the response to match other endpoints
    const formattedChannel = {
      _id: createdChannel._id.toString(), // Ensure _id is a string
      id: createdChannel._id.toString(),   // Also provide id field for compatibility
      name: createdChannel.name,
      type: createdChannel.type,
      contextType: createdChannel.contextType,
      contextId: createdChannel.contextId,
      members: createdChannel.members,
      ownerId: createdChannel.ownerId,
      groupIds: createdChannel.groupIds,
      isInactive: createdChannel.isInactive,
      messageCount: createdChannel.messageCount,
      lastMessageAt: createdChannel.lastMessageAt,
      createdAt: createdChannel.createdAt,
      updatedAt: createdChannel.updatedAt
    };

    return {
      success: true,
      message: 'Channel created successfully',
      data: formattedChannel
    };

  } catch (error) {
    if (error instanceof ValidationError || error instanceof AuthorizationError) {
      throw error;
    }
    
    console.error('Unexpected error in createChannel:', error);
    throw new Error(`Failed to create channel: ${error.message}`);
  }
};
